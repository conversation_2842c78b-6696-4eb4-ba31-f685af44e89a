# frozen_string_literal: true

require "spec_helper"

RSpec.describe Sentry::Rails::Logger, type: :request do
  before do
    expect(described_class).to receive(:subscribe_tracing_events).and_call_original

    make_basic_app do |config|
      config.enable_logs = true
      config.traces_sample_rate = 1.0
      config.rails.structured_logging = true
      config.rails.structured_logging.attach_to = [:active_record]
    end
  end

  context "when structured logging is disabled" do
    before do
      if defined?(Sentry::Rails::Logger)
        Sentry::Rails::Logger.unsubscribe_tracing_events
      end

      make_basic_app do |config|
        config.enable_logs = true
        config.traces_sample_rate = 1.0
        config.rails.structured_logging = false
      end
    end

    it "does not capture structured logs" do
      get "/posts"

      Sentry.get_current_client.flush

      db_log_events = sentry_logs.select do |log_event|
        log_event[:body]&.include?("Database query")
      end

      expect(db_log_events).to be_empty
    end
  end

  context "when logs are disabled" do
    before do
      make_basic_app do |config|
        config.enable_logs = false
        config.traces_sample_rate = 1.0
        config.rails.structured_logging = true
        config.rails.structured_logging.attach_to = [:active_record]
      end
    end

    it "does not capture structured logs" do
      get "/posts"

      Sentry.get_current_client.flush

      expect(sentry_logs).to be_empty
    end
  end
end
